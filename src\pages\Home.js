import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import Navbar from "../components/layout/Navbar";
import RatingComponent from "../components/RatingComponent";
import { novelAPI } from "../services/api";

const Home = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userNovels, setUserNovels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [currentUser, setCurrentUser] = useState(null);

  useEffect(() => {
    // 检查用户登录状态
    const token = localStorage.getItem("token");
    const userData = localStorage.getItem("user");

    if (token && userData) {
      const user = JSON.parse(userData);
      setIsLoggedIn(true);
      setCurrentUser(user);
      fetchUserNovels();
    }
  }, []);

  const fetchUserNovels = async () => {
    try {
      setLoading(true);
      const response = await novelAPI.getUserNovels();
      setUserNovels(response.data);
    } catch (err) {
      setError("获取作品列表失败");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (novelId) => {
    if (window.confirm("确定要删除这篇小说吗？")) {
      try {
        await novelAPI.deleteNovel(novelId);
        // 重新获取用户小说列表
        fetchUserNovels();
      } catch (err) {
        setError(err.response?.data?.msg || "删除失败");
      }
    }
  };

  // 未登录用户显示欢迎页面
  if (!isLoggedIn) {
    return (
      <>
        <Navbar />
        <div className="container mt-5">
          <div className="jumbotron bg-light p-5 rounded">
            <h1 className="display-4">欢迎来到小说创作网</h1>
            <p className="lead">这是一个供作者创作和分享小说的平台。</p>
            <hr className="my-4" />
            <p>立即注册账号，开始您的创作之旅！</p>
            <Link to="/register" className="btn btn-primary btn-lg">
              立即注册
            </Link>
            <Link to="/novels" className="btn btn-secondary btn-lg ms-2">
              浏览作品
            </Link>
          </div>
        </div>
      </>
    );
  }

  // 登录用户显示自己的作品列表
  return (
    <>
      <Navbar />
      <div className="container mt-5">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h2>我的作品</h2>
          <Link to="/editor" className="btn btn-primary">
            创作新小说
          </Link>
        </div>

        {error && <div className="alert alert-danger">{error}</div>}

        {loading ? (
          <div className="text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">加载中...</span>
            </div>
          </div>
        ) : userNovels.length === 0 ? (
          <div className="alert alert-info">
            您还没有创作任何小说。
            <Link to="/editor" className="alert-link ms-2">
              立即开始创作
            </Link>
          </div>
        ) : (
          <div className="row">
            {userNovels.map((novel) => (
              <div className="col-md-6 col-lg-4 mb-4" key={novel.id}>
                <div className="card h-100">
                  <div className="card-body">
                    <h5 className="card-title">{novel.title}</h5>
                    <p className="card-text">
                      {novel.content.length > 100
                        ? `${novel.content.substring(0, 100)}...`
                        : novel.content}
                    </p>
                  </div>
                  <div className="card-footer bg-white border-top-0">
                    <small className="text-muted">
                      创建于: {new Date(novel.created_at).toLocaleDateString()}
                    </small>

                    {/* 评价组件 */}
                    <div className="my-3">
                      <RatingComponent
                        novelId={novel.id}
                        authorId={novel.user_id}
                        currentUserId={currentUser?.id}
                      />
                    </div>

                    <div className="mt-2">
                      <Link
                        to={`/novel/${novel.id}`}
                        className="btn btn-sm btn-outline-primary me-2"
                      >
                        查看详情
                      </Link>
                      <Link
                        to={`/editor/${novel.id}`}
                        className="btn btn-sm btn-outline-secondary me-2"
                      >
                        编辑
                      </Link>
                      <button
                        className="btn btn-sm btn-outline-danger"
                        onClick={() => handleDelete(novel.id)}
                      >
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default Home;
